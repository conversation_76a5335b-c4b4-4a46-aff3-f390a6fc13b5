import React, { useState, useEffect, useRef, useMemo } from "react";
import { useAlgorithm } from "../../context/AlgorithmContext";
import { Canvas, extend } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import * as THREE from "three";
import {
  Box,
  Paper,
  Typography,
  useTheme,
} from "@mui/material";

// Import algorithm registry
import { getAlgorithm } from "../../algorithms/AlgorithmRegistry";
import ErrorBoundary from "../ErrorBoundary";
import LoadingIndicator from "../LoadingIndicator/LoadingIndicator";

// Import visualizer configuration
import { getAlgorithmConfig } from "../../config/visualizerConfig";

// Import environment maps utility
import { getEnvironmentMap } from "../../utils/environmentMaps";

// Extend Three.js with necessary components
extend({
  OrbitControls,
  Mesh: THREE.Mesh,
  CylinderGeometry: THREE.CylinderGeometry,
  BoxGeometry: THREE.BoxGeometry,
  MeshStandardMaterial: THREE.MeshStandardMaterial,
  Color: THREE.Color,
  AmbientLight: THREE.AmbientLight,
  DirectionalLight: THREE.DirectionalLight,
  HemisphereLight: THREE.HemisphereLight,
});

const AlgorithmVisualizer = ({ algorithm, params = {}, sceneConfig = {} }) => {
  // We don't need to manage algorithm params here - controllers should handle this
  const algorithmParams = params;

  // Get the algorithm-specific configuration and merge with any provided sceneConfig
  // Use useMemo instead of useState to avoid unnecessary re-renders
  const visualizerConfig = useMemo(() => {
    const algorithmConfig = getAlgorithmConfig(algorithm);
    return {
      ...algorithmConfig,
      ...sceneConfig
    };
  }, [algorithm, sceneConfig]);

  // Use the shared algorithm state from context
  const {
    state, setState,
    step, setStep,
    totalSteps, setTotalSteps,
    steps, setSteps,
    setMovements, // Used in the useEffect dependency array
    algorithmArray, // Get the array directly from context
    setAlgorithmArray // Set the array in context
  } = useAlgorithm();
  const [loading, setLoading] = useState(true); // Add loading state

  // IMPORTANT: We no longer maintain a local copy of the array
  // Instead, we pass the context array directly to visualization components
  const theme = useTheme();
  // Reference to the WebGL renderer and scene
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);

  // Update scene background when theme changes
  useEffect(() => {
    if (rendererRef.current && sceneRef.current && theme) {
      const clearColor = theme.palette.background?.default || "#151a26";
      rendererRef.current.setClearColor(clearColor);
      sceneRef.current.background = new THREE.Color(clearColor);
    }
  }, [theme]);

  // Handle window resize events to ensure proper viewport calculations
  useEffect(() => {
    // Function to handle resize events
    const handleResize = () => {
      console.log('Window resize detected - updating viewport');

      // Force a re-render of the canvas
      if (rendererRef.current) {
        // Update renderer size
        rendererRef.current.setSize(
          rendererRef.current.domElement.clientWidth,
          rendererRef.current.domElement.clientHeight
        );

        // Update pixel ratio in case of display changes
        rendererRef.current.setPixelRatio(window.devicePixelRatio);
      }
    };

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Clean up event listeners on component unmount
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Get the algorithm from the registry
  const algorithmData = getAlgorithm(algorithm);

  // Log array and steps for debugging
  useEffect(() => {
    console.log('AlgorithmVisualizer - Current array from context:', algorithmArray);
    // console.log('AlgorithmVisualizer - Current steps:', steps);
  }, [algorithmArray, steps]);

  // Reset state when algorithm changes
  useEffect(() => {
    // Skip on first render with empty params
    if (Object.keys(params).length === 0 && algorithm === '') {
      return;
    }

    // Reset all state variables to their initial values
    setStep(0);
    setState('idle');
    setTotalSteps(0);
    setSteps([]);
    setMovements([]);
    setAlgorithmArray([]); // Reset the context array
    setLoading(true); // Set loading to true when algorithm changes

    console.log(`AlgorithmVisualizer - Reset state for algorithm: ${algorithm}`);
  }, [algorithm]); // Only depend on algorithm changes

  // Set loading to false after a delay to simulate loading time
  useEffect(() => {
    if (loading) {
      const timer = setTimeout(() => {
        // Removed log
        setLoading(false);
      }, 1500); // 1.5 second delay for better user experience

      return () => clearTimeout(timer);
    }
  }, [loading, algorithm]);

  // We now handle all param changes in the first effect

  // Update canvas clear color when theme changes
  useEffect(() => {
    if (rendererRef.current) {
      const clearColor = theme.palette.background?.default || "#151a26";
      rendererRef.current.setClearColor(clearColor);
    }
  }, [theme.palette.mode]);

  // REMOVED: This useEffect was causing infinite loops

  // We now handle parameter changes through props and the first useEffect

  if (!algorithmData) {
    return (
      <Paper elevation={3} sx={{ p: 2, flexGrow: 1 }}>
        <Typography variant="h6" color="error">
          Algorithm "{algorithm}" not found
        </Typography>
      </Paper>
    );
  }

  // Get the visualization component for this algorithm
  const AlgorithmComponent = algorithmData.visualization;

  if (!AlgorithmComponent) {
    return (
      <Paper elevation={3} sx={{ p: 2, flexGrow: 1 }}>
        <Typography variant="body1">
          Visualization for "{algorithmData.name}" is not implemented yet.
        </Typography>
      </Paper>
    );
  }

  // We use the algorithm name directly as part of the key for components
  return (
    <Paper
      elevation={3}
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        overflow: "hidden", // Add overflow hidden to parent Paper if needed
        flexGrow: 1, // Ensure this Paper takes all available height
      }}
    >
      <Box
        sx={{
          flex: 1, // Allows this Box to take remaining height
          position: "relative",
          display: "flex",
          flexDirection: "row",
          height: "100%", // Ensure this row takes full height of its container
          width: "100%", // Take full width
          overflow: "hidden", // Prevent this Box from overflowing its parent Paper
        }}
      >
        {/* Main Canvas | Occupying middle section */}
        <Box sx={{
          flex: 1,
          position: "relative",
          height: "100%",
          width: "100%", // Ensure full width
          p: 0, // Remove padding to maximize space
          transition: "all 0.1s ease", // Smooth transition for all changes
          overflow: "hidden", // Prevent content overflow
          display: "flex", // Use flex display
          flexDirection: "column", // Stack children vertically
        }}>
          {/* Loading Indicator - Always on top */}
          <LoadingIndicator loading={loading} />

          {/* Ensure canvas container takes full height */}
          <ErrorBoundary
            onReset={() => {
              setStep(0);
              setState('idle');
            }}
            showDetails={false}
          >
            {/* Only render Canvas when not loading to prevent blue flash */}
            {!loading ? (
              <Canvas
                camera={{
                  position: visualizerConfig.camera.position,
                  fov: visualizerConfig.camera.fov,
                  near: visualizerConfig.camera.near,
                  far: visualizerConfig.camera.far
                }}
                style={{
                  width: "100%",
                  height: "100%",
                  flex: 1,
                  display: "block",
                }}
                gl={{
                  antialias: visualizerConfig.rendering.antialias,
                  pixelRatio: visualizerConfig.rendering.pixelRatio
                }}
                frameloop="always" /* Ensure animation loop always runs */
                shadows={visualizerConfig.rendering.shadows}
                onCreated={({ gl, scene, camera }) => {
                  // Store reference to the renderer and scene
                  rendererRef.current = gl;
                  sceneRef.current = scene;

                  // Use theme color for clear color if available, else fallback
                  const clearColor = theme.palette.background?.default || "#151a26";
                  gl.setClearColor(clearColor);

                  // Configure shadows based on settings
                  if (visualizerConfig.rendering.shadows) {
                    gl.shadowMap.enabled = true;

                    // Set shadow map type based on config
                    switch (visualizerConfig.rendering.shadowMapType) {
                      case "PCFSoft":
                        gl.shadowMap.type = THREE.PCFSoftShadowMap;
                        break;
                      case "PCF":
                        gl.shadowMap.type = THREE.PCFShadowMap;
                        break;
                      case "VSM":
                        gl.shadowMap.type = THREE.VSMShadowMap;
                        break;
                      default:
                        gl.shadowMap.type = THREE.BasicShadowMap;
                    }
                  }

                  // Set scene background color immediately to prevent blue flash
                  scene.background = new THREE.Color(clearColor);

                  // Set camera to look at the specified point
                  if (visualizerConfig.camera.lookAt) {
                    camera.lookAt(
                      visualizerConfig.camera.lookAt[0],
                      visualizerConfig.camera.lookAt[1],
                      visualizerConfig.camera.lookAt[2]
                    );
                  }
                }}
                // Add key prop to ensure Canvas remounts when algorithm changes
                key={`${algorithm}-canvas-${JSON.stringify(visualizerConfig.camera.position)}`}
              >
                {/* Orbit Controls */}
                {visualizerConfig.controls.orbit.enabled && (
                  <OrbitControls
                    enableDamping={visualizerConfig.controls.orbit.enableDamping}
                    dampingFactor={visualizerConfig.controls.orbit.dampingFactor}
                    enableZoom={visualizerConfig.controls.orbit.enableZoom}
                    enableRotate={visualizerConfig.controls.orbit.enableRotate}
                    enablePan={visualizerConfig.controls.orbit.enablePan}
                    autoRotate={visualizerConfig.controls.orbit.autoRotate}
                  />
                )}

                {/* Environment */}
                {visualizerConfig.environment.enabled && (
                  <Environment files={getEnvironmentMap(visualizerConfig.environment.preset)} />
                )}

                {/* Ambient Light */}
                {visualizerConfig.lighting.ambient.enabled && (
                  <ambientLight intensity={visualizerConfig.lighting.ambient.intensity} />
                )}

                {/* Directional Lights */}
                {visualizerConfig.lighting.directional.map((light, index) => {
                  if (!light.enabled) return null;

                  // Determine light color based on theme if dynamic color is enabled
                  const lightColor = light.useDynamicColor
                    ? (theme.palette.mode === 'dark' ? '#4fc3f7' : '#ffb74d')
                    : light.color;

                  return (
                    <directionalLight
                      key={`dir-light-${index}`}
                      position={light.position}
                      intensity={light.intensity}
                      color={lightColor}
                      castShadow={light.castShadow}
                      shadow-mapSize-width={light.shadowMapSize?.[0] || 2048}
                      shadow-mapSize-height={light.shadowMapSize?.[1] || 2048}
                      shadow-camera-left={light.shadowCameraBounds?.[0] || -10}
                      shadow-camera-right={light.shadowCameraBounds?.[1] || 10}
                      shadow-camera-top={light.shadowCameraBounds?.[2] || 10}
                      shadow-camera-bottom={light.shadowCameraBounds?.[3] || -10}
                      shadow-camera-near={light.shadowCameraNearFar?.[0] || 0.1}
                      shadow-camera-far={light.shadowCameraNearFar?.[1] || 50}
                      shadow-bias={light.shadowBias || -0.001}
                    />
                  );
                })}

                {/* Hemisphere Light */}
                {visualizerConfig.lighting.hemisphere.enabled && (
                  <hemisphereLight
                    skyColor={visualizerConfig.lighting.hemisphere.useDynamicColors
                      ? (theme.palette.mode === 'dark' ? '#303030' : '#ffffff')
                      : visualizerConfig.lighting.hemisphere.skyColor || '#ffffff'}
                    groundColor={visualizerConfig.lighting.hemisphere.useDynamicColors
                      ? (theme.palette.mode === 'dark' ? '#000000' : '#bbbbbb')
                      : visualizerConfig.lighting.hemisphere.groundColor || '#000000'}
                    intensity={visualizerConfig.lighting.hemisphere.intensity}
                  />
                )}

                {/* Spot Light */}
                {visualizerConfig.lighting.spot.enabled && (
                  <spotLight
                    position={visualizerConfig.lighting.spot.position}
                    angle={visualizerConfig.lighting.spot.angle}
                    penumbra={visualizerConfig.lighting.spot.penumbra}
                    intensity={visualizerConfig.lighting.spot.intensity}
                    castShadow={visualizerConfig.lighting.spot.castShadow}
                    shadow-mapSize-width={visualizerConfig.lighting.spot.shadowMapSize?.[0] || 1024}
                    shadow-mapSize-height={visualizerConfig.lighting.spot.shadowMapSize?.[1] || 1024}
                    shadow-bias={visualizerConfig.lighting.spot.shadowBias || -0.001}
                  />
                )}
                {/* Only render the algorithm component if it exists */}
                {/* Debug log to check if array is available */}
                {console.log('AlgorithmVisualizer - Rendering AlgorithmComponent with array:', algorithmArray)}
                {console.log('AlgorithmVisualizer - Current step:', step)}
                {console.log('AlgorithmVisualizer - Current state:', state)}
                {console.log('AlgorithmVisualizer - Total steps:', totalSteps)}
                {AlgorithmComponent ? (
                  <AlgorithmComponent
                    key={`${algorithm}-${algorithmArray ? algorithmArray.length : 0}-visualization`} // Use algorithm name and array size as key
                    params={algorithmParams}
                    state={state}
                    step={typeof step === 'object' ? JSON.stringify(step) : step}
                    setStep={setStep}
                    totalSteps={totalSteps}
                    setTotalSteps={setTotalSteps}
                    setState={setState}
                    theme={theme}
                    steps={steps} // Pass the centralized steps from context
                    setSteps={setSteps}
                    setMovements={setMovements}
                    array={algorithmArray} // Pass the array directly from context
                    sceneConfig={visualizerConfig} // Pass the scene configuration
                  />
                ) : (
                  // Don't render anything if AlgorithmComponent doesn't exist
                  null
                )}
              </Canvas>
            ) : null}
          </ErrorBoundary>
        </Box>

        {/* Controller is now rendered in App.js */}
      </Box>
    </Paper>
  );
};

export default AlgorithmVisualizer;
