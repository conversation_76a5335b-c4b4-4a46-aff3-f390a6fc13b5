// StepsSequenceSection.js
// A reusable steps sequence section component with consistent styling

import React, { useState, useRef, useEffect } from 'react';
import { Box, Typography, Stack, Paper, Collapse, useTheme } from '@mui/material';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';
import PropTypes from 'prop-types';

/**
 * A reusable steps sequence section component with consistent styling
 *
 * @param {Object} props - Component props
 * @param {Array} props.steps - Array of step objects with description and optional highlight
 * @param {number} props.currentStep - Current step index
 * @param {string} props.title - Optional custom title for the section (defaults to "Steps Sequence")
 * @param {boolean} props.defaultExpanded - Whether the section is expanded by default
 * @param {function} props.renderStep - Optional custom render function for each step
 * @param {Object} props.emptyMessage - Optional message to display when there are no steps
 */
const StepsSequenceSection = ({
  steps = [],
  currentStep = 0,
  title = 'Steps Sequence',
  defaultExpanded = true,
  renderStep,
  emptyMessage = 'No steps yet. Start the algorithm to see the sequence.',
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(defaultExpanded);
  const containerRef = useRef(null);

  // Auto-scroll to the current step
  useEffect(() => {
    if (containerRef.current && steps.length > 0 && currentStep > 0) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [currentStep, steps.length]);

  // Default step renderer
  const defaultRenderStep = (step, index) => {
    const isCurrentStep = index === currentStep - 1;

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          bgcolor: isCurrentStep ? (theme.palette.mode === 'dark' ? 'rgba(25, 118, 210, 0.2)' : 'rgba(25, 118, 210, 0.1)') : 'transparent',
          py: 0.5,
          pl: 0.5,
          pr: 0, // Remove right padding
          borderRadius: 1,
          mb: 0.5,
          overflowX: 'auto',
          whiteSpace: 'nowrap',
          '&::-webkit-scrollbar': {
            height: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
            borderRadius: '2px',
          },
        }}
      >
        <Typography
          variant="body2"
          component="div"
          sx={{
            fontFamily: 'ui-monospace, SFMono-Regular, "Courier New", monospace',
            fontSize: '0.85rem',
            fontWeight: isCurrentStep ? 'bold' : 'normal',
            whiteSpace: 'nowrap',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Box
            component="span"
            sx={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              minWidth: '20px',
              height: '20px',
              borderRadius: '10px',
              bgcolor: isCurrentStep ? theme.palette.success.main : (theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'),
              color: isCurrentStep ? theme.palette.success.contrastText : theme.palette.text.secondary,
              mr: 1,
              fontSize: '0.75rem',
              fontWeight: 'bold',
              flexShrink: 0,
            }}
          >
            {index + 1}
          </Box>
          {step.description}
        </Typography>
      </Box>
    );
  };

  // Use custom renderer if provided, otherwise use default
  const stepRenderer = renderStep || defaultRenderStep;

  return (
    <Paper elevation={1} sx={{
      p: 1,
      borderRadius: 1,
      mb: 1,
    }}>
      <Box
        sx={{
          position: 'relative',
          mb: expanded ? 1 : 0,
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          py: 0.25,
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant="body1" sx={{ fontWeight: 500, display: 'flex', alignItems: 'center' }}>
          <FormatListNumberedIcon fontSize="small" sx={{ mr: 0.5 }} /> {title}
        </Typography>
        <Box
          sx={{
            ml: 'auto',
            fontSize: '0.8rem',
            color: theme.palette.text.secondary
          }}
        >
          {expanded ? '▼' : '▶'}
        </Box>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{
          py: 1.5,
          pl: 1.5,
          pr: 0, // Remove right padding
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          borderRadius: 1,
          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
        }}>
          <Box
            ref={containerRef}
            sx={{
              maxHeight: '150px',
              overflowY: 'auto',
              pr: 0, // Remove right padding
              '&::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              },
            }}
          >
            {steps.length > 0 && currentStep > 0 ? (
              <Stack spacing={0.5}>
                {steps.slice(0, currentStep).map((step, index) => (
                  <div key={index}>{stepRenderer(step, index)}</div>
                ))}
              </Stack>
            ) : (
              <Typography variant="body2" sx={{ fontStyle: 'italic', fontSize: '0.85rem' }}>
                {emptyMessage}
              </Typography>
            )}
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

StepsSequenceSection.propTypes = {
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      description: PropTypes.string.isRequired,
      highlight: PropTypes.bool,
    })
  ),
  currentStep: PropTypes.number,
  title: PropTypes.string,
  defaultExpanded: PropTypes.bool,
  renderStep: PropTypes.func,
  emptyMessage: PropTypes.string,
};

export default StepsSequenceSection;
