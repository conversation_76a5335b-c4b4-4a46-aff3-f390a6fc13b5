// RadixSortSimulation.js
// Simulation component for RadixSort algorithm visualization

import React, { useMemo, useState, useRef } from 'react';
import { useTheme } from '@mui/material/styles';
import { Paper, Typography } from '@mui/material';
import { useFrame } from '@react-three/fiber';
import RadixSortConfig from '../../../algorithms/Sorting/RadixSort/RadixSortConfig';

// Import ThemeHtml for consistent label rendering
import ThemeHtml from '../ThemeHtml';

/**
 * Bucket component for RadixSort visualization
 */
const RadixSortBucket = ({
  position,
  bucketIndex,
  items = [],
  isActive = false,
  colors,
  theme
}) => {
  const config = RadixSortConfig.buckets;

  const bucketColor = isActive ? colors.activeBucket : colors.bucket;

  return (
    <group position={position}>
      {/* Bucket walls */}
      {/* Back wall */}
      <mesh position={[0, config.bucket.height / 2, -config.bucket.depth / 2]} castShadow receiveShadow>
        <boxGeometry args={[config.bucket.width, config.bucket.height, config.bucket.wallThickness]} />
        <meshStandardMaterial color={bucketColor} />
      </mesh>

      {/* Left wall */}
      <mesh position={[-config.bucket.width / 2, config.bucket.height / 2, 0]} castShadow receiveShadow>
        <boxGeometry args={[config.bucket.wallThickness, config.bucket.height, config.bucket.depth]} />
        <meshStandardMaterial color={bucketColor} />
      </mesh>

      {/* Right wall */}
      <mesh position={[config.bucket.width / 2, config.bucket.height / 2, 0]} castShadow receiveShadow>
        <boxGeometry args={[config.bucket.wallThickness, config.bucket.height, config.bucket.depth]} />
        <meshStandardMaterial color={bucketColor} />
      </mesh>

      {/* Bottom */}
      <mesh position={[0, config.bucket.wallThickness / 2, 0]} castShadow receiveShadow>
        <boxGeometry args={[config.bucket.width, config.bucket.wallThickness, config.bucket.depth]} />
        <meshStandardMaterial color={bucketColor} />
      </mesh>

      {/* Bucket label */}
      <ThemeHtml
        position={[
          config.labels.offset[0],
          config.labels.offset[1],
          config.labels.offset[2]
        ]}
        center
        sprite
        occlude
        theme={theme}
      >
        <Paper
          elevation={config.labels.elevation}
          sx={{
            px: theme.spacing(config.labels.padding.horizontal),
            py: theme.spacing(config.labels.padding.vertical),
            minWidth: config.labels.minWidth,
            borderRadius: theme.shape.borderRadius / config.labels.borderRadius,
            border: 1,
            borderColor: isActive ? 'primary.main' : 'divider',
            bgcolor: 'background.paper',
            userSelect: 'none',
            pointerEvents: 'none'
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontSize: config.labels.fontSize,
              textAlign: 'center',
              display: 'block',
              fontWeight: isActive ? 'bold' : config.labels.fontWeight
            }}
            color="text.primary"
          >
            {bucketIndex}
          </Typography>
        </Paper>
      </ThemeHtml>

      {/* Items in bucket */}
      {config.items.enabled && items.slice(0, config.items.maxVisible).map((item, index) => (
        <group
          key={`bucket-${bucketIndex}-item-${index}`}
          position={[
            (index - (items.length - 1) / 2) * config.items.spacing,
            config.items.offset[1] + index * config.items.spacing,
            config.items.offset[2]
          ]}
          scale={[config.items.scale, config.items.scale, config.items.scale]}
        >
          <mesh castShadow receiveShadow>
            <boxGeometry args={[0.3, 0.3, 0.3]} />
            <meshStandardMaterial color={colors.bucketItem} />
          </mesh>

          {/* Item value label */}
          {config.items.valueLabels.enabled && (
            <ThemeHtml
              position={config.items.valueLabels.offset}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={config.items.valueLabels.elevation}
                sx={{
                  px: theme.spacing(config.items.valueLabels.padding.horizontal),
                  py: theme.spacing(config.items.valueLabels.padding.vertical),
                  minWidth: config.items.valueLabels.minWidth,
                  borderRadius: theme.shape.borderRadius / config.items.valueLabels.borderRadius,
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: 'divider',
                  userSelect: 'none',
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: config.items.valueLabels.fontSize,
                    fontWeight: config.items.valueLabels.fontWeight,
                    textAlign: 'center',
                    display: 'block'
                  }}
                  color="text.primary"
                >
                  {item.value}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      ))}

      {/* Show count if more items than visible */}
      {config.items.countLabels.enabled && items.length > config.items.maxVisible && (
        <ThemeHtml
          position={[
            config.items.countLabels.offset[0],
            config.bucket.height + config.items.countLabels.offset[1],
            config.items.countLabels.offset[2]
          ]}
          center
          sprite
          occlude
          theme={theme}
        >
          <Typography
            variant="caption"
            sx={{
              fontSize: config.items.countLabels.fontSize,
              fontWeight: config.items.countLabels.fontWeight
            }}
            color={config.items.countLabels.color}
          >
            +{items.length - config.items.maxVisible} more
          </Typography>
        </ThemeHtml>
      )}
    </group>
  );
};

/**
 * Main RadixSort simulation component
 */
const RadixSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight = 4,
  barWidth = 0.8,
  barSpacing = 1.2,
  showValues = true,
  showIndices = true
}) => {
  const theme = useTheme();
  const bucketConfig = RadixSortConfig.buckets;

  // Animation state management
  const [moveAnimation, setMoveAnimation] = useState({
    active: false,
    fromPosition: [0, 0, 0],
    toPosition: [0, 0, 0],
    element: null,
    progress: 0,
    startTime: 0,
    type: 'to_bucket' // 'to_bucket' or 'from_bucket'
  });

  // State management for proper animation timing
  const [displayState, setDisplayState] = useState(null);

  // Refs for animation tracking
  const animatingRef = useRef(false);

  // Get configuration for positioning
  const config = RadixSortConfig;

  // Get visualization data from the current step
  const vizData = currentStep?.visualizationData;

  // Debug logging to understand what's happening at final step
  if (currentStep?.type === 'complete' || !vizData || !vizData.mainArray || !vizData.mainArray.values || vizData.mainArray.values.length === 0) {
    console.log('RadixSortSimulation - DEBUGGING FINAL STEP:');
    console.log('  currentStep:', currentStep);
    console.log('  currentStep.type:', currentStep?.type);
    console.log('  vizData:', vizData);
    console.log('  vizData.mainArray:', vizData?.mainArray);
    console.log('  vizData.mainArray.values:', vizData?.mainArray?.values);
    console.log('  vizData.mainArray.values.length:', vizData?.mainArray?.values?.length);
  }

  // Extract data from step
  const stepData = {
    mainArray: vizData?.mainArray || {},
    buckets: vizData?.buckets || [],
    digitPlace: vizData?.digitPlace || -1,
  };

  // For RadixSort, we need special handling during collection phase
  // During collection, we need to show progressive state rather than final state
  const getProgressiveCollectionState = () => {
    const stepType = currentStep?.type;

    // Handle all collection-related steps
    if (stepType === 'place_in_array' || stepType === 'collect_bucket_start' || stepType === 'collect_bucket_end') {
      const metadata = currentStep.metadata;

      console.log('currentStep:', currentStep);

      // For place_in_array steps, show only items collected up to current position
      let maxCollectedIndex = -1;
      if (stepType === 'place_in_array') {
        maxCollectedIndex = metadata.arrayIndex;
      } else if (stepType === 'collect_bucket_end') {
        // For collect_bucket_end, show all items collected so far including this bucket
        // The step data should show the final array state after collecting from this bucket
        const currentValues = stepData.mainArray.values;
        const currentBucketIndex = metadata.bucketIndex;

        // Count items that should be collected from buckets 0 to currentBucketIndex (inclusive)
        let itemsCollectedUpToThisBucket = 0;
        for (let i = 0; i <= currentBucketIndex; i++) {
          if (stepData.buckets && stepData.buckets[i]) {
            itemsCollectedUpToThisBucket += stepData.buckets[i].length;
          }
        }

        maxCollectedIndex = itemsCollectedUpToThisBucket - 1; // -1 because index is 0-based

        console.log('collect_bucket_end calculation:', {
          currentBucketIndex,
          itemsCollectedUpToThisBucket,
          maxCollectedIndex,
          currentValues: currentValues.slice(0, 10) // Show first 10 for debugging
        });
      } else if (stepType === 'collect_bucket_start') {
        // For collect_bucket_start, show items collected before this bucket
        // We need to find how many items were collected before starting this bucket
        const currentBucketIndex = metadata.bucketIndex || 0;

        // Count items that should be collected from buckets 0 to currentBucketIndex-1
        let itemsCollectedBefore = 0;
        for (let i = 0; i < currentBucketIndex; i++) {
          if (stepData.buckets && stepData.buckets[i]) {
            itemsCollectedBefore += stepData.buckets[i].length;
          }
        }

        maxCollectedIndex = itemsCollectedBefore - 1; // -1 because index is 0-based

        console.log('collect_bucket_start calculation:', {
          currentBucketIndex,
          itemsCollectedBefore,
          maxCollectedIndex
        });
      }

      // Create progressive main array (only show collected items so far)
      const progressiveValues = new Array(stepData.mainArray.values?.length || 0).fill(null);
      for (let i = 0; i <= maxCollectedIndex; i++) {
        if (stepData.mainArray.values && stepData.mainArray.values[i] !== undefined) {
          progressiveValues[i] = stepData.mainArray.values[i];
        }
      }

      console.log('Progressive values created:', {
        maxCollectedIndex,
        progressiveValues: progressiveValues.slice(0, 10), // Show first 10
        originalValues: stepData.mainArray.values?.slice(0, 10) // Show first 10
      });

      // For buckets, we need to simulate the progressive removal
      // Start with original buckets and remove items that have been collected
      const modifiedBuckets = stepData.buckets?.map((bucket, bucketIndex) => {
        const currentBucketIndex = metadata.bucketIndex || 0;

        if (stepType === 'place_in_array') {
          // During place_in_array, remove the specific item being placed from the specific bucket
          if (bucketIndex === currentBucketIndex) {
            return bucket.filter(item => item.value !== metadata.element);
          }
          // For other buckets, apply the same logic as collect_bucket_end
          return bucketIndex > currentBucketIndex ? bucket : [];
        } else if (stepType === 'collect_bucket_end') {
          // After bucket collection ends, buckets 0 to currentBucketIndex should be empty
          // Keep items only in buckets with index > currentBucketIndex
          return bucketIndex > currentBucketIndex ? bucket : [];
        } else {
          // collect_bucket_start - buckets before current should be empty, current bucket still has items
          return bucketIndex >= currentBucketIndex ? bucket : [];
        }
      }) || [];

      console.log('Bucket filtering result:', {
        stepType,
        currentBucketIndex: metadata.bucketIndex || 0,
        originalBucketLengths: stepData.buckets?.map(b => b.length),
        modifiedBucketLengths: modifiedBuckets.map(b => b.length)
      });

      return {
        mainArray: {
          ...stepData.mainArray,
          values: progressiveValues
        },
        buckets: modifiedBuckets,
        digitPlace: stepData.digitPlace
      };
    }

    return stepData;
  };

  // Use display state during animations, otherwise use progressive collection state
  const activeData = (moveAnimation.active && displayState) ? displayState : getProgressiveCollectionState();

  const {
    mainArray = {},
    buckets = [],
    digitPlace = -1,
  } = activeData;

  const {
    values = [],
    currentElement = -1,
    currentBucket = -1,
    sortedIndices = [],
  } = mainArray;

  // Debug logging for bucket visibility
  // console.log('RadixSortSimulation - Data Debug:', {
  //   stepType: currentStep?.type,
  //   stepMetadata: currentStep?.metadata,
  //   stepDataMainArray: stepData.mainArray.values,
  //   activeDataMainArray: activeData.mainArray.values,
  //   digitPlace,
  //   bucketsLength: buckets.length,
  //   buckets,
  //   displayState,
  //   moveAnimationActive: moveAnimation.active,
  //   mainArrayValues: values,
  //   maxCollectedIndex: 'will be calculated in getProgressiveCollectionState'
  // });

  // Handle algorithm completion state (like other sorting algorithms)
  const isComplete = currentStep?.type === 'complete';

  // Step-based animation detection (like InsertionSort and SelectionSort)
  React.useEffect(() => {
    if (!currentStep) return;

    const stepType = currentStep.type;
    const shouldAnimate = config.animation.enabled &&
      (stepType === 'place_in_bucket' || stepType === 'place_in_array');

    console.log('RadixSort step detection:', {
      stepType,
      shouldAnimate,
      animatingRef: animatingRef.current,
      currentStepId: currentStep.id
    });

    if (shouldAnimate && !animatingRef.current) {
      console.log('Starting animation for step:', stepType);

      // For bucket-to-platform animations, create modified state with item removed from bucket
      if (stepType === 'place_in_array') {
        const metadata = currentStep.metadata;
        const arrayIndex = metadata.arrayIndex;
        const bucketIndex = metadata.bucketIndex;
        const element = metadata.element;

        // Create progressive main array (only show collected items so far, excluding current)
        const progressiveValues = new Array(vizData?.mainArray?.values?.length).fill(null);
        for (let i = 0; i < arrayIndex; i++) {
          if (vizData?.mainArray?.values && vizData.mainArray.values[i] !== undefined) {
            progressiveValues[i] = vizData.mainArray.values[i];
          }
        }

        // Create modified buckets with the current item removed from source bucket
        const modifiedBuckets = vizData?.buckets?.map((bucket, index) => {
          if (index === bucketIndex && bucket.length > 0) {
            // Remove the first item that matches the element being moved
            const bucketCopy = [...bucket];
            const itemIndex = bucketCopy.findIndex(item => item.value === element);
            if (itemIndex !== -1) {
              bucketCopy.splice(itemIndex, 1);
            }
            return bucketCopy;
          }
          return [...bucket];
        }) || [];

        // Set display state with modified buckets and progressive array during animation
        setDisplayState({
          mainArray: {
            ...vizData?.mainArray,
            values: progressiveValues
          },
          buckets: modifiedBuckets,
          digitPlace: vizData?.digitPlace || -1,
        });
      }

      // Start animation for this step
      startMoveAnimation(currentStep);
    } else if (!shouldAnimate && !animatingRef.current) {
      // For non-animation steps, update display state immediately
      setDisplayState({
        mainArray: vizData?.mainArray || {},
        buckets: vizData?.buckets || [],
        digitPlace: vizData?.digitPlace || -1,
      });
    }

    // Initialize display state if null (first load)
    if (displayState === null && !animatingRef.current) {
      setDisplayState({
        mainArray: vizData?.mainArray || {},
        buckets: vizData?.buckets || [],
        digitPlace: vizData?.digitPlace || -1,
      });
    }
  }, [currentStep, config.animation.enabled, vizData]);

  // Function to start move animation
  const startMoveAnimation = (step) => {
    if (!step || !config.animation.move.enabled) {
      console.log('Animation not started:', { step: !!step, enabled: config.animation.move.enabled });
      return;
    }

    const stepType = step.type;
    const metadata = step.metadata || {};

    console.log('startMoveAnimation called:', { stepType, metadata });

    if (stepType === 'place_in_bucket') {
      // Animation from main array to bucket
      const elementIndex = metadata.currentIndex || 0;
      const bucketIndex = metadata.digit || 0; // Use 'digit' field for bucket index
      const element = metadata.element;

      // Calculate source position (main array)
      const sourcePos = calculateMainArrayPosition(elementIndex);

      // Calculate target position (bucket)
      const targetPos = calculateBucketPosition(bucketIndex);

      console.log('Starting place_in_bucket animation:', {
        elementIndex,
        bucketIndex,
        element,
        sourcePos,
        targetPos
      });

      animatingRef.current = true;
      setMoveAnimation({
        active: true,
        fromPosition: sourcePos,
        toPosition: targetPos,
        element: { index: elementIndex, value: element },
        progress: 0,
        startTime: performance.now(),
        type: 'to_bucket'
      });

    } else if (stepType === 'place_in_array') {
      // Animation from bucket to main array
      const arrayIndex = metadata.arrayIndex;
      const bucketIndex = metadata.bucketIndex;
      const element = metadata.element;

      // Calculate source position (bucket)
      const sourcePos = calculateBucketPosition(bucketIndex);

      // Calculate target position (main array)
      const targetPos = calculateMainArrayPosition(arrayIndex);

      console.log('Starting place_in_array animation:', {
        arrayIndex,
        bucketIndex,
        element,
        sourcePos,
        targetPos
      }); 

      animatingRef.current = true;
      setMoveAnimation({
        active: true,
        fromPosition: sourcePos,
        toPosition: targetPos,
        element: { index: arrayIndex, value: element },
        progress: 0,
        startTime: performance.now(),
        type: 'from_bucket'
      });
    }
  };

  // Helper function to calculate main array position
  const calculateMainArrayPosition = (index) => {
    const totalBarsWidth = values.length * barWidth;
    const totalSpacing = (values.length - 1) * barSpacing;
    const totalWidth = totalBarsWidth + totalSpacing;
    const startX = -totalWidth / 2;
    const xPos = startX + index * (barWidth + barSpacing) + barWidth / 2;
    return [xPos, 0, 0];
  };

  // Helper function to calculate bucket position
  const calculateBucketPosition = (bucketIndex) => {
    if (bucketIndex < 0 || bucketIndex >= bucketPositions.length) {
      return [0, 0, 0];
    }
    return bucketPositions[bucketIndex];
  };

  // Animation frame update
  useFrame(() => {
    if (moveAnimation.active) {
      const elapsed = performance.now() - moveAnimation.startTime;
      const duration = config.animation.move.duration;
      const progress = Math.min(elapsed / duration, 1);

      setMoveAnimation(prev => ({
        ...prev,
        progress: progress
      }));

      // End animation when complete (like other algorithms)
      if (progress >= 1) {
        console.log('RadixSort animation completed, type:', moveAnimation.type);

        // For bucket-to-platform animations, update to show the item on platform
        // For platform-to-bucket animations, use the current step's data
        if (moveAnimation.type === 'from_bucket') {
          // Update display state to show the item now on platform
          const metadata = currentStep.metadata || {};
          const arrayIndex = metadata.arrayIndex || 0;
          const bucketIndex = metadata.bucketIndex || 0;
          const element = metadata.element;

          // Create progressive main array including the just-placed item
          const progressiveValues = new Array(vizData?.mainArray?.values?.length || 0).fill(null);
          for (let i = 0; i <= arrayIndex; i++) {
            if (vizData?.mainArray?.values && vizData.mainArray.values[i] !== undefined) {
              progressiveValues[i] = vizData.mainArray.values[i];
            }
          }

          // Create updated buckets with the item removed
          const updatedBuckets = [...(displayState.buckets || [])];
          if (updatedBuckets[bucketIndex]) {
            // Remove the first item from the bucket
            updatedBuckets[bucketIndex] = updatedBuckets[bucketIndex].filter((item, idx) =>
              idx !== 0 // Remove only the first item (which was animated)
            );
          }

          // Keep the modified buckets (with item removed)
          setDisplayState(prev => ({
            ...prev,
            mainArray: {
              ...prev.mainArray,
              values: progressiveValues
            },
            buckets: updatedBuckets
          }));

          console.log('Updated progressive state after place_in_array animation:', {
            updatedBuckets,
            progressiveValues
          });
        } else {
          // For place_in_bucket animations, update to current step's data
          setDisplayState({
            mainArray: vizData?.mainArray || {},
            buckets: vizData?.buckets || [],
            digitPlace: vizData?.digitPlace || -1,
          });
        }

        // End the animation
        setMoveAnimation(prev => ({
          ...prev,
          active: false
        }));
        animatingRef.current = false;
      }
    }
  });

  // Calculate bar positions using passed props (like other sorting algorithms)
  const barPositions = useMemo(() => {
    if (!values.length) return [];

    const totalBarsWidth = values.length * barWidth;
    const totalSpacing = (values.length - 1) * barSpacing;
    const totalWidth = totalBarsWidth + totalSpacing;
    const startX = -totalWidth / 2;

    return values.map((value, index) => {
      const xPos = startX + index * (barWidth + barSpacing) + barWidth / 2;
      return {
        index,
        value,
        xPos,
        isCurrent: currentElement === index,
        isNull: value === null || value === undefined, // Track null values
      };
    });
  }, [values, currentElement, barWidth, barSpacing]);

  // Calculate bucket positions based on arrangement type
  const bucketPositions = useMemo(() => {
    const positions = [];

    const { type, radius, angleRange, startAngle, position } = bucketConfig.arrangement;

    for (let i = 0; i < 10; i++) {
      let x, y, z;

      switch (type) {
        case 'semicircle':
          // Semicircle arrangement (original implementation)
          const angle = startAngle + (i / 9) * angleRange;
          x = radius * Math.cos(angle);
          z = position[2] + radius * Math.sin(angle);
          y = position[1];
          break;

        case 'line':
          // Linear arrangement (horizontal line)
          const spacing = 2.0; // Spacing between buckets in line
          x = (i - 4.5) * spacing; // Center the line around x=0
          y = position[1];
          z = position[2];
          break;

        case 'grid':
          // Grid arrangement (2 rows of 5 buckets each)
          const gridSpacing = 2.5; // Spacing between buckets in grid
          const row = Math.floor(i / 5); // 0 or 1
          const col = i % 5; // 0 to 4
          x = (col - 2) * gridSpacing; // Center columns around x=0
          y = position[1];
          z = position[2] + row * gridSpacing; // Offset rows in z direction
          break;

        default:
          // Fallback to semicircle if type is not recognized
          console.warn(`Unknown bucket arrangement type: ${type}. Using semicircle.`);
          const fallbackAngle = startAngle + (i / 9) * angleRange;
          x = radius * Math.cos(fallbackAngle);
          z = position[2] + radius * Math.sin(fallbackAngle);
          y = position[1];
      }

      positions.push([
        x + position[0], // Add base position offset
        y,
        z
      ]);
    }

    return positions;
  }, [bucketConfig]);

  // Render main array bars using standard approach (like SelectionSort)
  const renderMainArray = () => {
    if (!values.length) return null;

    const maxValue = Math.max(...values);
    const barConfig = config.mainArray.bars;
    const valueLabelsConfig = config.mainArray.valueLabels;
    const indexLabelsConfig = config.mainArray.indexLabels;

    return barPositions.map(({ index, value, xPos, isCurrent, isNull }) => {
      // For null values (not yet collected), only show index label
      if (isNull) {
        return (
          <group key={`main-${index}-null`} position={[xPos, 0, 0]}>
            {/* Index label for not-yet-collected items */}
            {showIndices && indexLabelsConfig.enabled && (
              <ThemeHtml
                position={[
                  indexLabelsConfig.offset[0],
                  indexLabelsConfig.offset[1],
                  indexLabelsConfig.offset[2]
                ]}
                center
                sprite
                occlude
                theme={theme}
              >
                <Paper
                  elevation={indexLabelsConfig.elevation}
                  sx={{
                    width: indexLabelsConfig.size.width,
                    height: indexLabelsConfig.size.height,
                    borderRadius: '50%',
                    bgcolor: 'background.paper',
                    border: 1,
                    borderColor: 'divider',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    opacity: 0.3, // Faded for not-yet-collected
                  }}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: indexLabelsConfig.fontSize,
                      fontWeight: indexLabelsConfig.fontWeight,
                      opacity: 0.5,
                    }}
                    color="text.disabled"
                  >
                    {index}
                  </Typography>
                </Paper>
              </ThemeHtml>
            )}
          </group>
        );
      }

      // Validate values to prevent NaN in BoxGeometry
      const safeValue = isNaN(value) ? 0 : Math.max(0, value);
      const safeMaxValue = isNaN(maxValue) || maxValue <= 0 ? 1 : maxValue;
      const safeMaxBarHeight = isNaN(maxBarHeight) || maxBarHeight <= 0 ? 4 : maxBarHeight;

      const barHeight = (safeValue / safeMaxValue) * safeMaxBarHeight;
      const isInCurrentBucket = currentBucket >= 0;
      const isSorted = isComplete || sortedIndices.includes(index);

      // Hide bar if it's currently being animated
      const isBeingAnimated = moveAnimation.active &&
        moveAnimation.element &&
        moveAnimation.element.index === index;

      // Check if element is in bucket (using activeData which reflects current display state)
      // During RadixSort, if an element is in a bucket, it should not appear on platform
      const isInBucket = buckets.some(bucket =>
        bucket.some(item => item.originalIndex === index)
      );

      // For place_in_array steps, the element should appear on platform (not hidden)
      const stepType = currentStep?.type;
      const isPlaceInArrayStep = stepType === 'place_in_array';
      const shouldHideFromPlatform = isInBucket && !isPlaceInArrayStep;

      // Don't render the bar if it's being animated
      if (isBeingAnimated) {
        return null;
      }

      // Determine bar color based on state (like other sorting algorithms)
      let barColor;
      if (isSorted) {
        barColor = colors.sorted;
      } else if (isCurrent) {
        barColor = colors.current;
      } else if (isInCurrentBucket) {
        barColor = colors.bucketItem;
      } else {
        barColor = colors.bar;
      }

      return (
        <group key={`main-${index}-${value}`} position={[xPos, 0, 0]}>
          {/* Only render bar and value label if element should be on platform */}
          {!shouldHideFromPlatform && (
            <>
              {/* Base of the bar (like SelectionSort and QuickSort) */}
              {barConfig.base.enabled && (
                <mesh
                  position={[0, 0, 0]}
                  castShadow
                  receiveShadow
                >
                  <boxGeometry args={[
                    barWidth * barConfig.base.widthScale,
                    barConfig.base.height,
                    barWidth * barConfig.base.depthScale
                  ]} />
                  <meshStandardMaterial
                    color={barColor}
                    transparent={barConfig.base.material.transparent}
                    opacity={barConfig.base.material.opacity}
                    roughness={barConfig.base.material.roughness}
                    metalness={barConfig.base.material.metalness}
                  />
                </mesh>
              )}

              {/* The bar itself (standard approach using config) */}
              <mesh
                position={[0, barHeight / 2 + (barConfig.base.enabled ? barConfig.base.height : 0), 0]}
                castShadow
                receiveShadow
              >
                <boxGeometry args={[
                  barWidth * barConfig.geometry.widthScale,
                  barHeight,
                  barWidth * barConfig.geometry.depthScale
                ]} />
                <meshStandardMaterial
                  color={barColor}
                  transparent={barConfig.material.transparent}
                  opacity={barConfig.material.opacity}
                  roughness={barConfig.material.roughness}
                  metalness={barConfig.material.metalness}
                />
              </mesh>

              {/* Value label (only show if not in bucket) */}
              {showValues && valueLabelsConfig.enabled && (
                <ThemeHtml
                  position={[
                    valueLabelsConfig.offset[0],
                    barHeight + valueLabelsConfig.offset[1] + (barConfig.base.enabled ? barConfig.base.height : 0),
                    valueLabelsConfig.offset[2]
                  ]}
                  center
                  sprite
                  occlude
                  theme={theme}
                >
                  <Paper
                    elevation={valueLabelsConfig.elevation}
                    sx={{
                      px: theme.spacing(valueLabelsConfig.padding.horizontal),
                      py: theme.spacing(valueLabelsConfig.padding.vertical),
                      minWidth: valueLabelsConfig.minWidth,
                      borderRadius: theme.shape.borderRadius / valueLabelsConfig.borderRadius,
                      border: 1,
                      borderColor: isSorted ? 'success.main' : (isCurrent ? 'primary.main' : 'divider'),
                      bgcolor: 'background.paper',
                      userSelect: 'none',
                      pointerEvents: 'none'
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: valueLabelsConfig.fontSize,
                        textAlign: 'center',
                        display: 'block',
                        fontWeight: (isSorted || isCurrent) ? 'bold' : valueLabelsConfig.fontWeight
                      }}
                      color="text.primary"
                    >
                      {value}
                    </Typography>
                  </Paper>
                </ThemeHtml>
              )}
            </>
          )}

          {/* Index label (always show, but with faded style if not collected yet) */}
          {showIndices && indexLabelsConfig.enabled && (
            <ThemeHtml
              position={[
                indexLabelsConfig.offset[0],
                indexLabelsConfig.offset[1],
                indexLabelsConfig.offset[2]
              ]}
              center
              sprite
              occlude
              theme={theme}
            >
              <Paper
                elevation={indexLabelsConfig.elevation}
                sx={{
                  width: indexLabelsConfig.size.width,
                  height: indexLabelsConfig.size.height,
                  borderRadius: '50%',
                  bgcolor: 'background.paper',
                  border: 1,
                  borderColor: isSorted ? 'success.main' : (isCurrent ? 'primary.main' : 'divider'),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  userSelect: 'none',
                  pointerEvents: 'none',
                  // Fade the index label if element is not on platform
                  opacity: shouldHideFromPlatform ? 0.3 : 1,
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: indexLabelsConfig.fontSize,
                    fontWeight: (isSorted || isCurrent) ? 'bold' : indexLabelsConfig.fontWeight,
                    // Fade the text if element is not on platform
                    opacity: shouldHideFromPlatform ? 0.5 : 1,
                  }}
                  color={shouldHideFromPlatform ? 'text.disabled' : 'text.primary'}
                >
                  {index}
                </Typography>
              </Paper>
            </ThemeHtml>
          )}
        </group>
      );
    });
  };

  // Render animated bar during move animation
  const renderAnimatedBar = () => {
    if (!moveAnimation.active || !moveAnimation.element) return null;

    const { element, fromPosition, toPosition, progress, type } = moveAnimation;
    const { value } = element;

    // Calculate current position with arc movement
    const easedProgress = 0.5 - 0.5 * Math.cos(progress * Math.PI); // Smooth easing

    const currentX = fromPosition[0] + (toPosition[0] - fromPosition[0]) * easedProgress;
    const currentZ = fromPosition[2] + (toPosition[2] - fromPosition[2]) * easedProgress;

    // Add arc height for natural movement
    const arcHeight = config.animation.move.arcHeight;
    const arcProgress = Math.sin(progress * Math.PI);
    const currentY = fromPosition[1] + (toPosition[1] - fromPosition[1]) * easedProgress + arcHeight * arcProgress;

    // Calculate bar height based on the configuration
    const safeValues = values.filter(v => v !== null && v !== undefined);
    const maxValue = safeValues.length > 0 ? Math.max(...safeValues) : 1;
    const safeValue = value !== undefined && value !== null ? Math.max(0, value) : 0;
    const safeMaxValue = Math.max(1, maxValue);
    const safeMaxBarHeight = Math.max(0.1, maxBarHeight);

    // Calculate the full-sized bar height (what it would be on the platform)
    const fullBarHeight = Math.min((safeValue / safeMaxValue) * safeMaxBarHeight, safeMaxBarHeight);

    // Define the size ratio between bucket items and platform items
    const bucketSizeRatio = 0.5; // Bucket items are 50% the size of platform items

    // Determine bar height based on animation type
    let barHeight;
    if (type === 'to_bucket') {
      // When moving to bucket, start with full height and end with reduced height
      barHeight = Math.min(fullBarHeight * (1 - (easedProgress * (1 - bucketSizeRatio))), safeMaxBarHeight);
    } else {
      // When moving from bucket, start with reduced height and end with full height
      barHeight = Math.min(fullBarHeight * (bucketSizeRatio + (easedProgress * (1 - bucketSizeRatio))), safeMaxBarHeight);
    }

    // Calculate scaled dimensions for width and depth
    const startWidthScale = type === 'to_bucket' ? 1.0 : bucketSizeRatio;
    const endWidthScale = type === 'to_bucket' ? bucketSizeRatio : 1.0;
    const currentWidthScale = startWidthScale + (endWidthScale - startWidthScale) * easedProgress;

    const scaledBarWidth = barWidth * config.mainArray.bars.geometry.widthScale * currentWidthScale;
    const scaledBarDepth = barWidth * config.mainArray.bars.geometry.depthScale * currentWidthScale;

    // Determine bar color based on animation type
    const barColor = type === 'to_bucket' ? colors.current : colors.bucketItem;

    // Calculate label scale factor
    const labelScaleFactor = startWidthScale + (endWidthScale - startWidthScale) * easedProgress;

    return (
      <group position={[currentX, currentY, currentZ]}>
        {/* Animated bar - Position Y offset is half of the current height */}
        <mesh
          position={[0, barHeight / 2, 0]}
          castShadow
          receiveShadow
        >
          <boxGeometry args={[
            scaledBarWidth,
            barHeight,
            scaledBarDepth
          ]} />
          <meshStandardMaterial
            color={barColor}
            transparent={config.mainArray.bars.material.transparent}
            opacity={config.mainArray.bars.material.opacity}
            roughness={config.mainArray.bars.material.roughness}
            metalness={config.mainArray.bars.material.metalness}
          />
        </mesh>

        {/* Animated value label - Position adjusted based on current bar height */}
        {showValues && config.mainArray.valueLabels.enabled && (
          <ThemeHtml
            position={[0, barHeight + config.mainArray.valueLabels.offset[1] * labelScaleFactor, 0]}
            center
            sprite
            occlude
            theme={theme}
            scale={[labelScaleFactor, labelScaleFactor, labelScaleFactor]}
          >
            <Paper
              elevation={config.mainArray.valueLabels.elevation}
              sx={{
                px: theme.spacing(config.mainArray.valueLabels.padding.horizontal),
                py: theme.spacing(config.mainArray.valueLabels.padding.vertical),
                minWidth: config.mainArray.valueLabels.minWidth,
                borderRadius: theme.shape.borderRadius / config.mainArray.valueLabels.borderRadius,
                border: 1,
                borderColor: 'primary.main',
                bgcolor: 'background.paper',
                userSelect: 'none',
                pointerEvents: 'none'
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  fontSize: config.mainArray.valueLabels.fontSize,
                  textAlign: 'center',
                  display: 'block',
                  fontWeight: 'bold'
                }}
                color="text.primary"
              >
                {value}
              </Typography>
            </Paper>
          </ThemeHtml>
        )}
      </group>
    );
  };

  return (
    <group>
      {/* Main array bars using standard rendering */}
      {renderMainArray()}

      {/* Buckets (RadixSort-specific feature) - Always show buckets during RadixSort */}
      {/* {console.log('Bucket rendering check:', {
        digitPlace,
        bucketPositionsLength: bucketPositions.length,
        bucketsData: buckets,
        isComplete
      })} */}
      {!isComplete && bucketPositions.map((position, bucketIndex) => {
        // console.log(`Rendering bucket ${bucketIndex}:`, {
        //   position,
        //   items: buckets[bucketIndex] || [],
        //   isActive: currentBucket === bucketIndex
        // });
        return (
          <RadixSortBucket
            key={`bucket-${bucketIndex}`}
            position={position}
            bucketIndex={bucketIndex}
            items={buckets[bucketIndex] || []}
            isActive={currentBucket === bucketIndex}
            colors={colors}
            theme={theme}
          />
        );
      })}

      {/* Animated bar during move animation */}
      {renderAnimatedBar()}
    </group>
  );
};

export default RadixSortSimulation;
