// RadixSortVisualization.js
// Visualization component for RadixSort algorithm following the new architecture

import React, { useEffect, useRef, useMemo } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';

// Import configuration and components
import CONFIG from './RadixSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

// Import standard visualization components (like other sorting algorithms)
import { FixedStepBoard, FixedColorLegend, SortingBase } from '../../../components/visualization';
import RadixSortSimulation from '../../../components/visualization/bars/RadixSortSimulation';

/**
 * Main RadixSort visualization component using new architecture
 */
const RadixSortVisualization = () => {
  const theme = useTheme();
  const { camera } = useThree();
  const { state, setState, step, setStep, steps } = useAlgorithm();
  const { speed } = useSpeed();

  // Refs for state management and levitation
  const speedRef = useRef(speed);
  const animatingRef = useRef(false);
  const timeoutIdRef = useRef(null);
  const groupRef = useRef();

  // Get colors from configuration based on theme
  const colors = useMemo(() => {
    const isDark = theme?.palette?.mode === 'dark';
    return isDark ? CONFIG.colors.themes.dark : CONFIG.colors.themes.light;
  }, [theme]);

  // Update speed ref when speed changes
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Auto-play functionality and levitation animation
  useFrame(({ clock }) => {
    // Auto-play functionality
    if (state === 'running' && !animatingRef.current && steps && steps.length > 0) {
      if (step < steps.length - 1) {
        animatingRef.current = true;

        const delay = getEnhancedDelay(speedRef.current);

        timeoutIdRef.current = setTimeout(() => {
          setStep(prev => prev + 1);
          animatingRef.current = false;
        }, delay);
      } else {
        // Algorithm completed
        setState('completed');
      }
    }

    // Levitation animation - same pattern as other sorting algorithms
    if (CONFIG.visual.levitation.enabled &&
      (!CONFIG.visual.levitation.disableDuringSimulation || state === 'idle') &&
      groupRef.current) {

      const time = clock.getElapsedTime();
      const levitationConfig = CONFIG.visual.levitation;

      // Apply levitation relative to base platform position from config
      const basePosition = CONFIG.basePlatform.position;

      if (levitationConfig.movement.y.enabled) {
        groupRef.current.position.y = basePosition[1] + Math.sin(time * levitationConfig.movement.y.frequency) * levitationConfig.movement.y.amplitude;
      } else {
        groupRef.current.position.y = basePosition[1];
      }

      if (levitationConfig.movement.x.enabled) {
        groupRef.current.position.x = basePosition[0] + Math.sin(time * levitationConfig.movement.x.frequency) * levitationConfig.movement.x.amplitude;
      } else {
        groupRef.current.position.x = basePosition[0];
      }

      if (levitationConfig.movement.z.enabled) {
        groupRef.current.position.z = basePosition[2] + Math.sin(time * levitationConfig.movement.z.frequency) * levitationConfig.movement.z.amplitude;
      } else {
        groupRef.current.position.z = basePosition[2];
      }

      // Apply rotation effects
      if (levitationConfig.rotation.enabled) {
        if (levitationConfig.rotation.x.enabled) {
          groupRef.current.rotation.x = Math.cos(time * levitationConfig.rotation.x.frequency) * levitationConfig.rotation.x.amplitude;
        }

        if (levitationConfig.rotation.y.enabled) {
          groupRef.current.rotation.y = Math.sin(time * levitationConfig.rotation.y.frequency) * levitationConfig.rotation.y.amplitude;
        }

        if (levitationConfig.rotation.z.enabled) {
          groupRef.current.rotation.z = Math.sin(time * levitationConfig.rotation.z.frequency) * levitationConfig.rotation.z.amplitude;
        }
      }
    }
  });

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
    };
  }, []);

  // Position camera on mount using config
  useEffect(() => {
    if (camera) {
      const cameraConfig = CONFIG.camera;
      camera.position.set(...cameraConfig.position);
      camera.lookAt(...cameraConfig.lookAt);
      camera.fov = cameraConfig.fov;
      camera.updateProjectionMatrix();
    }
  }, [camera]);

  // Get current step data for rendering with fallback handling
  let currentStepData = null;

  if (steps && steps.length > 0) {
    if (step >= 0 && step < steps.length) {
      // Normal case: step is within bounds
      currentStepData = steps[step];
    } else if (step >= steps.length) {
      // Step is beyond array bounds - use the last step (completion state)
      console.log('RadixSortVisualization - Step beyond bounds, using last step');
      console.log('  step:', step, 'steps.length:', steps.length);
      currentStepData = steps[steps.length - 1];
    } else if (step === 0 && steps.length > 0) {
      // Step 0 - use first step for initial state
      currentStepData = steps[0];
    }
  }

  // Debug logging for step data issues
  if (!currentStepData) {
    console.log('RadixSortVisualization - No currentStepData after fallback:');
    console.log('  step:', step);
    console.log('  steps.length:', steps?.length);
    console.log('  steps:', steps);
  }

  // Create legend items with current colors (like SelectionSort)
  const legendItems = useMemo(() => {
    return CONFIG.colorLegend.legendItems.map(item => ({
      ...item,
      color: colors[item.colorKey] || colors.bar
    }));
  }, [colors]);

  // Calculate stage dimensions for platform (like SelectionSort)
  const stageDimensions = useMemo(() => {
    const arrayLength = currentStepData?.mainArray?.values?.length || 10;
    const adaptiveBarWidth = CONFIG.mainArray.bars.width;
    const adaptiveSpacing = CONFIG.mainArray.bars.spacing;
    const totalBarsWidth = (arrayLength * (adaptiveBarWidth + adaptiveSpacing)) - adaptiveSpacing;

    return {
      width: Math.max(
        totalBarsWidth + CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right,
        CONFIG.basePlatform.dimensions.lengthPadding.left + CONFIG.basePlatform.dimensions.lengthPadding.right + 4
      ),
      height: CONFIG.basePlatform.dimensions.height,
      depth: CONFIG.basePlatform.dimensions.depth
    };
  }, [currentStepData]);

  return (
    <>
      {/* Step board - using standard component */}
      {CONFIG.stepBoard.enabled && (
        <FixedStepBoard
          currentStep={step > 0 ? step : ''}
          totalSteps={steps && steps.length > 0 ? steps.length - 1 : 0}
          description={step === 0 ?
            `Radix Sort: Initial Array [${currentStepData?.visualizationData?.mainArray?.values?.join(', ') || ''}]` :
            (currentStepData?.statement || 'Radix Sort Algorithm')}
          stepData={currentStepData}
          showStepNumber={step > 0}
          position={CONFIG.stepBoard.position}
          width={CONFIG.stepBoard.dimensions.width}
          height={CONFIG.stepBoard.dimensions.height}
          theme={theme}
        />
      )}

      {/* Color legend - using standard component */}
      {CONFIG.colorLegend.enabled && (
        <FixedColorLegend
          items={legendItems}
          position={CONFIG.colorLegend.position}
          itemSpacing={CONFIG.colorLegend.itemSpacing}
          theme={theme}
        />
      )}

      {/* Main Array Group - positioned like other sorting algorithms with levitation */}
      <group ref={groupRef} position={CONFIG.basePlatform.position}>
        {/* Base platform - using standard component */}
        <SortingBase
          width={stageDimensions.width}
          height={stageDimensions.height}
          depth={stageDimensions.depth}
          color={colors.platform}
          position={[0, -stageDimensions.height / 2, 0]}
        />

        {/* RadixSort simulation - positioned relative to the base platform */}
        <group position={CONFIG.mainArray.bars.baseOffset}>
          <RadixSortSimulation
            currentStep={currentStepData}
            colors={colors}
            maxBarHeight={CONFIG.mainArray.bars.maxHeight}
            barWidth={CONFIG.mainArray.bars.width}
            barSpacing={CONFIG.mainArray.bars.spacing}
            showValues={true}
            showIndices={true}
          />
        </group>
      </group>

      {/* Standard lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={0.8}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-20}
        shadow-camera-right={20}
        shadow-camera-top={20}
        shadow-camera-bottom={-20}
      />
      <directionalLight
        position={[-10, 5, -5]}
        intensity={0.3}
      />
    </>
  );
};

export default RadixSortVisualization;
