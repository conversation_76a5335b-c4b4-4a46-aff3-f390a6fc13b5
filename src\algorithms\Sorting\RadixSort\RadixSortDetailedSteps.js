// RadixSortDetailedSteps.js
// Detailed step generation for RadixSort algorithm

/**
 * Generates detailed steps for the RadixSort algorithm
 * @param {Array} inputArray - The array to sort
 * @returns {Object} - Object containing steps and metadata
 */
export const generateRadixSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const steps = [];
  let stepCounter = 0;

  // Helper function to create a step
  const createStep = (type, statement, visualizationData, metadata = {}) => {
    return {
      id: stepCounter++,
      type,
      statement,
      visualizationData,
      metadata: {
        algorithm: 'RadixSort',
        ...metadata
      }
    };
  };

  // Helper function to get digit at specific position
  const getDigit = (num, digitPlace) => {
    return Math.floor(Math.abs(num) / Math.pow(10, digitPlace)) % 10;
  };

  // Helper function to get number of digits
  const getDigitCount = (num) => {
    if (num === 0) return 1;
    return Math.floor(Math.log10(Math.abs(num))) + 1;
  };

  // Initialize visualization data
  let currentElement = -1;
  let currentDigitPlace = -1;
  let currentBucket = -1;
  let buckets = Array.from({ length: 10 }, () => []);
  let sortedIndices = [];

  // Step 0: Initial state
  steps.push(createStep(
    'initial',
    'RadixSort: Starting with the initial array',
    {
      mainArray: {
        values: [...arr],
        currentElement,
        currentDigitPlace,
        currentBucket,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))), // Ensure deep copy of items if any initially
      digitPlace: -1,
      maxDigits: 0,
    },
    { isInitial: true }
  ));

  // Find maximum number to determine number of digits
  const maxNumber = Math.max(...arr.map(num => Math.abs(num)));
  const maxDigits = getDigitCount(maxNumber);

  // Step 1: Show maximum number and digit count
  steps.push(createStep(
    'find_max',
    `Found maximum number: ${maxNumber} with ${maxDigits} digit${maxDigits > 1 ? 's' : ''}`,
    {
      mainArray: {
        values: [...arr],
        currentElement: arr.indexOf(maxNumber),
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
      digitPlace: -1,
      maxDigits,
    },
    { maxNumber, maxDigits }
  ));

  // Process each digit place (from least significant to most significant)
  for (let digitPlace = 0; digitPlace < maxDigits; digitPlace++) {
    steps.push(createStep(
      'digit_start',
      `Processing digit place ${digitPlace + 1} (10^${digitPlace}) - ${digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}`} place`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
        digitPlace,
        maxDigits,
      },
      { digitPlace, digitName: digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}` }
    ));

    // Clear buckets for this digit place (actual data structure)
    buckets = Array.from({ length: 10 }, () => []);

    // Distribute elements into buckets based on current digit
    for (let i = 0; i < arr.length; i++) {
      const element = arr[i];
      const digit = getDigit(element, digitPlace);

      steps.push(createStep(
        'process_element',
        `Processing element ${element} at index ${i}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: -1,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit }
      ));

      steps.push(createStep(
        'extract_digit',
        `Digit at position ${digitPlace + 1} of ${element} is ${digit}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, digitPlace }
      ));

      buckets[digit].push({ value: element, originalIndex: i });

      steps.push(createStep(
        'place_in_bucket',
        `Placed ${element} in bucket ${digit}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          // Show buckets after the element is placed
          buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, bucketSize: buckets[digit].length }
      ));
    }

    steps.push(createStep(
      'buckets_filled',
      `All elements distributed into buckets for digit place ${digitPlace + 1}`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
        digitPlace,
        maxDigits,
      },
      { digitPlace, totalElements: arr.length }
    ));

    // Collect elements back from buckets in order
    let arrayIndex = 0;
    for (let bucketIndex = 0; bucketIndex < 10; bucketIndex++) {
      if (buckets[bucketIndex].length > 0) {
        steps.push(createStep(
          'collect_bucket_start',
          `Collecting ${buckets[bucketIndex].length} element${buckets[bucketIndex].length > 1 ? 's' : ''} from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: [...arr], // arr state before collecting from this bucket
              currentElement: -1,
              currentDigitPlace: digitPlace,
              currentBucket: bucketIndex,
              sortedIndices: [],
            },
            // Show buckets before starting to collect from this specific bucket
            buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
            digitPlace,
            maxDigits,
          },
          { bucketIndex, bucketSize: buckets[bucketIndex].length }
        ));

        // MODIFICATION START: Correctly process and visualize element removal from bucket
        const numElementsInBucket = buckets[bucketIndex].length;
        for (let k = 0; k < numElementsInBucket; k++) {
          const elementObject = buckets[bucketIndex].shift(); // Get and REMOVE element from the front of the bucket
          // buckets[bucketIndex] is now modified.

          arr[arrayIndex] = elementObject.value; // Place element in the main array 'arr'

          // Step: Show element being placed back in array
          // The visualizationData will use the updated state of 'arr' and 'buckets'
          steps.push(createStep(
            'place_in_array',
            `Placing ${elementObject.value} at position ${arrayIndex}`,
            {
              mainArray: {
                values: [...arr], // 'arr' now reflects the current placement for this pass
                currentElement: arrayIndex, // Highlight the element being placed in the main array
                currentDigitPlace: digitPlace,
                currentBucket: bucketIndex, // Highlight the bucket it came from
                sortedIndices: [],
              },
              // 'buckets.map' creates a snapshot of 'buckets' where 'buckets[bucketIndex]'
              // has had 'elementObject' removed by .shift().
              buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
              digitPlace,
              maxDigits,
            },
            { arrayIndex, element: elementObject.value, bucketIndex, originalIndex: elementObject.originalIndex }
          ));

          arrayIndex++;
        }
        // MODIFICATION END

        // Step: Finished collecting from current bucket
        // buckets[bucketIndex] is now empty.
        steps.push(createStep(
          'collect_bucket_end',
          `Finished collecting from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: [...arr], // arr is now updated with elements from this bucket
              currentElement: -1,
              currentDigitPlace: digitPlace,
              currentBucket: -1, // As per original logic, reset currentBucket after collection
              sortedIndices: [],
            },
            // buckets[bucketIndex] will be empty in this snapshot
            buckets: buckets.map(bucket => bucket.map(item => ({ ...item }))),
            digitPlace,
            maxDigits,
          },
          { bucketIndex }
        ));
      }
    }

    steps.push(createStep(
      'digit_complete',
      `Completed processing digit place ${digitPlace + 1}. Array after this pass: [${arr.join(', ')}]`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: -1, // Reset for the completion of the pass
          currentBucket: -1,
          sortedIndices: [], // Sorted indices are only final at the very end
        },
        buckets: Array.from({ length: 10 }, () => []), // Visually clear buckets for the next pass
        digitPlace: -1, // Reset for the completion of the pass
        maxDigits,
      },
      { digitPlace, completedPass: digitPlace + 1 }
    ));
  }

  // Mark all elements as sorted
  sortedIndices = Array.from({ length: arr.length }, (_, i) => i);

  // Final step: Algorithm complete
  steps.push(createStep(
    'complete',
    `RadixSort complete! Final sorted array: [${arr.join(', ')}]`,
    {
      mainArray: {
        values: [...arr],
        currentElement: -1,
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices, // All indices are now sorted
      },
      buckets: Array.from({ length: 10 }, () => []), // Buckets are empty
      digitPlace: -1,
      maxDigits,
    },
    { isComplete: true, sortedArray: [...arr] }
  ));

  return {
    steps,
    totalSteps: steps.length,
    sortedArray: arr,
    metadata: {
      algorithm: 'RadixSort',
      inputSize: inputArray.length,
      maxDigits,
      totalPasses: maxDigits,
    }
  };
};

export default generateRadixSortDetailedSteps;