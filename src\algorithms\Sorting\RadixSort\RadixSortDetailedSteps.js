// RadixSortDetailedSteps.js
// Detailed step generation for RadixSort algorithm

/**
 * Generates detailed steps for the RadixSort algorithm
 * @param {Array} inputArray - The array to sort
 * @returns {Object} - Object containing steps and metadata
 */
export const generateRadixSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const steps = [];
  let stepCounter = 0;

  // Helper function to create a step
  const createStep = (type, statement, visualizationData, metadata = {}) => {
    return {
      id: stepCounter++,
      type,
      statement,
      visualizationData,
      metadata: {
        algorithm: 'RadixSort',
        ...metadata
      }
    };
  };

  // Helper function to get digit at specific position
  const getDigit = (num, digitPlace) => {
    return Math.floor(Math.abs(num) / Math.pow(10, digitPlace)) % 10;
  };

  // Helper function to get number of digits
  const getDigitCount = (num) => {
    if (num === 0) return 1;
    return Math.floor(Math.log10(Math.abs(num))) + 1;
  };

  // Initialize visualization data
  let currentElement = -1;
  let currentDigitPlace = -1;
  let currentBucket = -1;
  let buckets = Array.from({ length: 10 }, () => []);
  let sortedIndices = [];

  // Step 0: Initial state
  steps.push(createStep(
    'initial',
    'RadixSort: Starting with the initial array',
    {
      mainArray: {
        values: [...arr],
        currentElement,
        currentDigitPlace,
        currentBucket,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => [...bucket]),
      digitPlace: -1,
      maxDigits: 0,
    },
    { isInitial: true }
  ));

  // Find maximum number to determine number of digits
  const maxNumber = Math.max(...arr.map(num => Math.abs(num)));
  const maxDigits = getDigitCount(maxNumber);

  // Step 1: Show maximum number and digit count
  steps.push(createStep(
    'find_max',
    `Found maximum number: ${maxNumber} with ${maxDigits} digit${maxDigits > 1 ? 's' : ''}`,
    {
      mainArray: {
        values: [...arr],
        currentElement: arr.indexOf(maxNumber),
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices: [],
      },
      buckets: buckets.map(bucket => [...bucket]),
      digitPlace: -1,
      maxDigits,
    },
    { maxNumber, maxDigits }
  ));

  // Process each digit place (from least significant to most significant)
  for (let digitPlace = 0; digitPlace < maxDigits; digitPlace++) {
    // Step: Start processing current digit place
    steps.push(createStep(
      'digit_start',
      `Processing digit place ${digitPlace + 1} (10^${digitPlace}) - ${digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}`} place`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => [...bucket]),
        digitPlace,
        maxDigits,
      },
      { digitPlace, digitName: digitPlace === 0 ? 'ones' : digitPlace === 1 ? 'tens' : digitPlace === 2 ? 'hundreds' : `10^${digitPlace}` }
    ));

    // Clear buckets for this digit place
    buckets = Array.from({ length: 10 }, () => []);

    // Distribute elements into buckets based on current digit
    for (let i = 0; i < arr.length; i++) {
      const element = arr[i];
      const digit = getDigit(element, digitPlace);

      // Step: Show current element being processed
      steps.push(createStep(
        'process_element',
        `Processing element ${element} at index ${i}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: -1,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => [...bucket]),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit }
      ));

      // Step: Show digit extraction
      steps.push(createStep(
        'extract_digit',
        `Digit at position ${digitPlace + 1} of ${element} is ${digit}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => [...bucket]),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, digitPlace }
      ));

      // Add element to appropriate bucket
      buckets[digit].push({ value: element, originalIndex: i });

      // Step: Show element placed in bucket
      steps.push(createStep(
        'place_in_bucket',
        `Placed ${element} in bucket ${digit}`,
        {
          mainArray: {
            values: [...arr],
            currentElement: i,
            currentDigitPlace: digitPlace,
            currentBucket: digit,
            sortedIndices: [],
          },
          buckets: buckets.map(bucket => [...bucket]),
          digitPlace,
          maxDigits,
        },
        { currentIndex: i, element, digit, bucketSize: buckets[digit].length }
      ));
    }

    // Step: Show all buckets filled
    steps.push(createStep(
      'buckets_filled',
      `All elements distributed into buckets for digit place ${digitPlace + 1}`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: digitPlace,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: buckets.map(bucket => [...bucket]),
        digitPlace,
        maxDigits,
      },
      { digitPlace, totalElements: arr.length }
    ));

    // Collect elements back from buckets in order
    let arrayIndex = 0;
    for (let bucketIndex = 0; bucketIndex < 10; bucketIndex++) {
      if (buckets[bucketIndex].length > 0) {
        // Step: Start collecting from current bucket
        steps.push(createStep(
          'collect_bucket_start',
          `Collecting ${buckets[bucketIndex].length} element${buckets[bucketIndex].length > 1 ? 's' : ''} from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: [...arr],
              currentElement: -1,
              currentDigitPlace: digitPlace,
              currentBucket: bucketIndex,
              sortedIndices: [],
            },
            buckets: buckets.map(bucket => [...bucket]),
            digitPlace,
            maxDigits,
          },
          { bucketIndex, bucketSize: buckets[bucketIndex].length }
        ));

        // Collect each element from the bucket
        for (let j = 0; j < buckets[bucketIndex].length; j++) {
          const element = buckets[bucketIndex][j];
          arr[arrayIndex] = element.value;

          // Step: Show element being placed back in array
          steps.push(createStep(
            'place_in_array',
            `Placing ${element.value} at position ${arrayIndex}`,
            {
              mainArray: {
                values: [...arr],
                currentElement: arrayIndex,
                currentDigitPlace: digitPlace,
                currentBucket: bucketIndex,
                sortedIndices: [],
              },
              buckets: buckets.map(bucket => [...bucket]),
              digitPlace,
              maxDigits,
            },
            { arrayIndex, element: element.value, bucketIndex }
          ));

          arrayIndex++;
        }

        // Step: Finished collecting from current bucket
        steps.push(createStep(
          'collect_bucket_end',
          `Finished collecting from bucket ${bucketIndex}`,
          {
            mainArray: {
              values: [...arr],
              currentElement: -1,
              currentDigitPlace: digitPlace,
              currentBucket: -1,
              sortedIndices: [],
            },
            buckets: buckets.map(bucket => [...bucket]),
            digitPlace,
            maxDigits,
          },
          { bucketIndex }
        ));
      }
    }

    // Step: Completed current digit place
    steps.push(createStep(
      'digit_complete',
      `Completed processing digit place ${digitPlace + 1}. Array after this pass: [${arr.join(', ')}]`,
      {
        mainArray: {
          values: [...arr],
          currentElement: -1,
          currentDigitPlace: -1,
          currentBucket: -1,
          sortedIndices: [],
        },
        buckets: Array.from({ length: 10 }, () => []), // Clear buckets
        digitPlace: -1,
        maxDigits,
      },
      { digitPlace, completedPass: digitPlace + 1 }
    ));
  }

  // Mark all elements as sorted
  sortedIndices = Array.from({ length: arr.length }, (_, i) => i);

  // Final step: Algorithm complete
  steps.push(createStep(
    'complete',
    `RadixSort complete! Final sorted array: [${arr.join(', ')}]`,
    {
      mainArray: {
        values: [...arr],
        currentElement: -1,
        currentDigitPlace: -1,
        currentBucket: -1,
        sortedIndices,
      },
      buckets: Array.from({ length: 10 }, () => []),
      digitPlace: -1,
      maxDigits,
    },
    { isComplete: true, sortedArray: [...arr] }
  ));

  return {
    steps,
    totalSteps: steps.length,
    sortedArray: arr,
    metadata: {
      algorithm: 'RadixSort',
      inputSize: inputArray.length,
      maxDigits,
      totalPasses: maxDigits,
    }
  };
};

export default generateRadixSortDetailedSteps;
